import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { api } from '@/lib/serverComm';
import { Star, MapPin, Phone, Globe } from 'lucide-react';

interface Business {
  id: number;
  name: string;
  slug: string;
  short_description: string;
  address: string;
  phone: string;
  website: string;
  logo_url: string;
  hero_image_url: string;
  is_featured: boolean;
  average_rating: string;
  total_reviews: number;
  category: {
    id: number;
    name: string;
    slug: string;
    icon: string;
  };
}

export function FeaturedBusinesses() {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchFeaturedBusinesses() {
      try {
        const response = await api.getBusinesses({ 
          featured: true, 
          limit: 6 
        });
        setBusinesses(response.businesses);
      } catch (err) {
        setError('Failed to load featured businesses');
        console.error('Error fetching featured businesses:', err);
      } finally {
        setLoading(false);
      }
    }

    fetchFeaturedBusinesses();
  }, []);

  if (loading) {
    return (
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Featured Businesses</h2>
            <p className="text-muted-foreground">Discover top-rated local businesses</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="h-48 bg-muted"></div>
                <CardContent className="p-6">
                  <div className="h-4 bg-muted rounded mb-2"></div>
                  <div className="h-3 bg-muted rounded mb-4"></div>
                  <div className="h-3 bg-muted rounded w-24"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-16 px-4">
        <div className="container mx-auto text-center">
          <p className="text-muted-foreground">{error}</p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 px-4">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Featured Businesses</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Discover exceptional local businesses that have earned our featured status 
            through outstanding service and customer satisfaction.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {businesses.map((business) => (
            <Link key={business.id} to={`/business/${business.slug}`} className="group">
              <Card className="h-full overflow-hidden transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
                {/* Business Image */}
                <div className="relative h-48 overflow-hidden">
                  {business.hero_image_url ? (
                    <img
                      src={business.hero_image_url}
                      alt={business.name}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                      <span className="text-2xl font-bold text-muted-foreground">
                        {business.name.charAt(0)}
                      </span>
                    </div>
                  )}
                  <Badge className="absolute top-3 left-3 bg-primary">
                    Featured
                  </Badge>
                  {business.logo_url && (
                    <div className="absolute bottom-3 right-3 w-12 h-12 rounded-full overflow-hidden border-2 border-background">
                      <img
                        src={business.logo_url}
                        alt={`${business.name} logo`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                </div>

                <CardContent className="p-6">
                  <div className="space-y-3">
                    {/* Business Name & Category */}
                    <div>
                      <h3 className="font-semibold text-lg group-hover:text-primary transition-colors line-clamp-1">
                        {business.name}
                      </h3>
                      <Badge variant="outline" className="text-xs mt-1">
                        {business.category.name}
                      </Badge>
                    </div>

                    {/* Rating */}
                    {business.total_reviews > 0 && (
                      <div className="flex items-center gap-2">
                        <div className="flex items-center">
                          <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                          <span className="text-sm font-medium ml-1">
                            {parseFloat(business.average_rating).toFixed(1)}
                          </span>
                        </div>
                        <span className="text-xs text-muted-foreground">
                          ({business.total_reviews} reviews)
                        </span>
                      </div>
                    )}

                    {/* Description */}
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {business.short_description}
                    </p>

                    {/* Contact Info */}
                    <div className="space-y-1 text-xs text-muted-foreground">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-3 h-3" />
                        <span className="line-clamp-1">{business.address}</span>
                      </div>
                      {business.phone && (
                        <div className="flex items-center gap-2">
                          <Phone className="w-3 h-3" />
                          <span>{business.phone}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {businesses.length > 0 && (
          <div className="text-center mt-8">
            <Button variant="outline" asChild>
              <Link to="/businesses">View All Businesses</Link>
            </Button>
          </div>
        )}
      </div>
    </section>
  );
}
