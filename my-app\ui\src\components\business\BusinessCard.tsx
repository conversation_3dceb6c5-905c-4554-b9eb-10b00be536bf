import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LazyImage } from '@/components/ui/lazy-image';
import { Star, MapPin, Phone, Globe } from 'lucide-react';

interface Business {
  id: number;
  name: string;
  slug: string;
  short_description: string;
  address: string;
  phone: string;
  website: string;
  logo_url: string;
  hero_image_url: string;
  is_featured: boolean;
  average_rating: string;
  total_reviews: number;
}

interface BusinessCardProps {
  business: Business;
  showCategory?: boolean;
  category?: {
    name: string;
    slug: string;
  };
}

export function BusinessCard({ business, showCategory = false, category }: BusinessCardProps) {
  return (
    <Link to={`/business/${business.slug}`} className="group">
      <Card className="h-full overflow-hidden transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
        {/* Business Image */}
        <div className="relative h-48 overflow-hidden">
          {business.hero_image_url ? (
            <LazyImage
              src={business.hero_image_url}
              alt={business.name}
              className="group-hover:scale-105 transition-transform duration-200"
              containerClassName="w-full h-full"
              fallback={`data:image/svg+xml;base64,${btoa(`<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#f3f4f6"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial" font-size="48" fill="#9ca3af">${business.name.charAt(0)}</text></svg>`)}`}
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
              <span className="text-2xl font-bold text-muted-foreground">
                {business.name.charAt(0)}
              </span>
            </div>
          )}
          
          {/* Featured Badge */}
          {business.is_featured && (
            <Badge className="absolute top-3 left-3 bg-primary">
              Featured
            </Badge>
          )}
          
          {/* Logo */}
          {business.logo_url && (
            <div className="absolute bottom-3 right-3 w-12 h-12 rounded-full overflow-hidden border-2 border-background">
              <LazyImage
                src={business.logo_url}
                alt={`${business.name} logo`}
                containerClassName="w-full h-full"
              />
            </div>
          )}
        </div>

        <CardContent className="p-6">
          <div className="space-y-3">
            {/* Business Name */}
            <div>
              <h3 className="h5 group-hover:text-primary transition-colors line-clamp-1">
                {business.name}
              </h3>
              {showCategory && category && (
                <Badge variant="outline" className="caption mt-1">
                  {category.name}
                </Badge>
              )}
            </div>

            {/* Rating */}
            {business.total_reviews > 0 && (
              <div className="flex items-center gap-2">
                <div className="flex items-center">
                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  <span className="label ml-1">
                    {parseFloat(business.average_rating).toFixed(1)}
                  </span>
                </div>
                <span className="caption">
                  ({business.total_reviews} {business.total_reviews === 1 ? 'review' : 'reviews'})
                </span>
              </div>
            )}

            {/* Description */}
            <p className="label text-muted-foreground line-clamp-2">
              {business.short_description}
            </p>

            {/* Contact Info */}
            <div className="space-y-1 caption">
              <div className="flex items-center gap-2">
                <MapPin className="w-3 h-3 flex-shrink-0" />
                <span className="line-clamp-1">{business.address}</span>
              </div>
              
              {business.phone && (
                <div className="flex items-center gap-2">
                  <Phone className="w-3 h-3 flex-shrink-0" />
                  <span>{business.phone}</span>
                </div>
              )}
              
              {business.website && (
                <div className="flex items-center gap-2">
                  <Globe className="w-3 h-3 flex-shrink-0" />
                  <span className="line-clamp-1">
                    {business.website.replace(/^https?:\/\//, '')}
                  </span>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}
