import { BusinessHeader } from './BusinessHeader';
import { ContactInfo } from './ContactInfo';
import { BusinessHours } from './BusinessHours';
import { PhotoGallery } from './PhotoGallery';
import { ReviewsSection } from './ReviewsSection';

interface BusinessData {
  id: number;
  name: string;
  slug: string;
  description: string;
  short_description: string;
  address: string;
  latitude: string;
  longitude: string;
  phone: string;
  email: string;
  website: string;
  logo_url: string;
  hero_image_url: string;
  is_featured: boolean;
  average_rating: string;
  total_reviews: number;
  created_at: string;
  category: {
    id: number;
    name: string;
    slug: string;
    icon: string;
    description: string;
  };
  hours: Array<{
    id: number;
    day_of_week: string;
    open_time: string;
    close_time: string;
    is_closed: boolean;
  }>;
  images: Array<{
    id: number;
    image_url: string;
    alt_text: string;
    is_primary: boolean;
    display_order: number;
  }>;
  reviews: Array<{
    id: number;
    rating: number;
    comment: string;
    author_name: string;
    created_at: string;
    is_verified: boolean;
  }>;
}

interface BusinessProfileProps {
  business: BusinessData;
}

export function BusinessProfile({ business }: BusinessProfileProps) {
  return (
    <div className="space-y-8">
      {/* Business Header */}
      <BusinessHeader business={business} />
      
      {/* Main Content */}
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content Column */}
          <div className="lg:col-span-2 space-y-8">
            {/* Description */}
            {business.description && (
              <section>
                <h2 className="text-2xl font-bold mb-4">About</h2>
                <div className="prose prose-neutral dark:prose-invert max-w-none">
                  <p className="text-muted-foreground leading-relaxed">
                    {business.description}
                  </p>
                </div>
              </section>
            )}
            
            {/* Photo Gallery */}
            {business.images && business.images.length > 0 && (
              <PhotoGallery images={business.images} businessName={business.name} />
            )}
            
            {/* Reviews */}
            <ReviewsSection 
              reviews={business.reviews} 
              businessId={business.id}
              averageRating={parseFloat(business.average_rating)}
              totalReviews={business.total_reviews}
            />
          </div>
          
          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Information */}
            <ContactInfo business={business} />
            
            {/* Business Hours */}
            {business.hours && business.hours.length > 0 && (
              <BusinessHours hours={business.hours} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
