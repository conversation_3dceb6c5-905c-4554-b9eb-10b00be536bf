import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Building, 
  Users, 
  Star, 
  TrendingUp, 
  Clock, 
  CheckCircle,
  AlertCircle,
  Plus
} from 'lucide-react';
import { Link } from 'react-router-dom';

export function AdminDashboard() {
  // Mock data - in real app, this would come from API
  const stats = {
    totalBusinesses: 156,
    pendingApplications: 8,
    totalReviews: 342,
    averageRating: 4.2,
    newBusinessesThisMonth: 12,
    activeUsers: 1250
  };

  const recentApplications = [
    {
      id: 1,
      businessName: "Joe's Coffee Shop",
      category: "Restaurant",
      submittedAt: "2024-01-08",
      status: "pending"
    },
    {
      id: 2,
      businessName: "Tech Solutions Inc",
      category: "Technology",
      submittedAt: "2024-01-07",
      status: "pending"
    },
    {
      id: 3,
      businessName: "Green Thumb Landscaping",
      category: "Home & Garden",
      submittedAt: "2024-01-06",
      status: "approved"
    }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your business directory and monitor performance
          </p>
        </div>
        <Button asChild>
          <Link to="/admin/applications">
            <Plus className="w-4 h-4 mr-2" />
            Review Applications
          </Link>
        </Button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Businesses</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalBusinesses}</div>
            <p className="text-xs text-muted-foreground">
              +{stats.newBusinessesThisMonth} this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Applications</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingApplications}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Reviews</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalReviews}</div>
            <p className="text-xs text-muted-foreground">
              Avg. {stats.averageRating} stars
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeUsers}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline w-3 h-3 mr-1" />
              +12% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Applications */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Applications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentApplications.map((application) => (
              <div key={application.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-1">
                  <h4 className="font-medium">{application.businessName}</h4>
                  <p className="text-sm text-muted-foreground">{application.category}</p>
                  <p className="text-xs text-muted-foreground">
                    Submitted {application.submittedAt}
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <Badge 
                    variant={application.status === 'approved' ? 'default' : 'secondary'}
                    className="capitalize"
                  >
                    {application.status === 'approved' ? (
                      <CheckCircle className="w-3 h-3 mr-1" />
                    ) : (
                      <AlertCircle className="w-3 h-3 mr-1" />
                    )}
                    {application.status}
                  </Badge>
                  {application.status === 'pending' && (
                    <Button size="sm" variant="outline">
                      Review
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 text-center">
            <Button variant="outline" asChild>
              <Link to="/admin/applications">View All Applications</Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Manage Businesses</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              View, edit, and manage all businesses in the directory.
            </p>
            <Button className="w-full" asChild>
              <Link to="/admin/businesses">Manage Businesses</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Add, edit, and organize business categories.
            </p>
            <Button className="w-full" variant="outline" asChild>
              <Link to="/admin/categories">Manage Categories</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Reviews</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Moderate and manage customer reviews.
            </p>
            <Button className="w-full" variant="outline" asChild>
              <Link to="/admin/reviews">Manage Reviews</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
