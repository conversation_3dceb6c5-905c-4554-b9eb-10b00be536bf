import 'dotenv/config';
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { authMiddleware, adminMiddleware } from './middleware/auth';
import { getDatabase, testDatabaseConnection } from './lib/db';
import { setEnvContext, clearEnvContext, getDatabaseUrl } from './lib/env';
import * as schema from './schema/users';
import * as businessSchema from './schema/business';
import { eq, and, like, desc, asc, count, sql } from 'drizzle-orm';
import adminRoutes from './routes/admin';
import businessRoutes from './routes/admin-businesses';
import categoryRoutes from './routes/admin-categories';
import reviewRoutes from './routes/admin-reviews';
import userRoutes from './routes/admin-users';
import applicationRoutes from './routes/admin-applications';

type Env = {
  RUNTIME?: string;
  [key: string]: any;
};

const app = new Hono<{ Bindings: Env }>();

// In Node.js environment, set environment context from process.env
if (typeof process !== 'undefined' && process.env) {
  setEnvContext(process.env);
}

// Environment context middleware - detect runtime using RUNTIME env var
app.use('*', async (c, next) => {
  if (c.env?.RUNTIME === 'cloudflare') {
    setEnvContext(c.env);
  }
  
  await next();
  // No need to clear context - env vars are the same for all requests
  // In fact, clearing the context would cause the env vars to potentially be unset for parallel requests
});

// Middleware
app.use('*', logger());
app.use('*', cors());

// Health check route - public
app.get('/', (c) => c.json({ status: 'ok', message: 'API is running' }));

// API routes
const api = new Hono();

// Public routes go here (if any)
api.get('/hello', (c) => {
  return c.json({
    message: 'Hello from Hono!',
  });
});

// ===== BUSINESS DIRECTORY API ROUTES =====

// Get all categories (public)
api.get('/categories', async (c) => {
  try {
    const db = await getDatabase();
    const categories = await db
      .select({
        id: businessSchema.categories.id,
        name: businessSchema.categories.name,
        slug: businessSchema.categories.slug,
        icon: businessSchema.categories.icon,
        description: businessSchema.categories.description,
        business_count: sql<number>`count(${businessSchema.businesses.id})`,
      })
      .from(businessSchema.categories)
      .leftJoin(businessSchema.businesses, and(
        eq(businessSchema.categories.id, businessSchema.businesses.category_id),
        eq(businessSchema.businesses.is_active, true)
      ))
      .where(eq(businessSchema.categories.is_active, true))
      .groupBy(
        businessSchema.categories.id,
        businessSchema.categories.name,
        businessSchema.categories.slug,
        businessSchema.categories.icon,
        businessSchema.categories.description
      )
      .orderBy(businessSchema.categories.name);

    return c.json({ categories });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return c.json({ error: 'Failed to fetch categories' }, 500);
  }
});

// Get all businesses (public)
api.get('/businesses', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const category = c.req.query('category');
    const search = c.req.query('search');
    const featured = c.req.query('featured') === 'true';
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    let query = db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        logo_url: businessSchema.businesses.logo_url,
        hero_image_url: businessSchema.businesses.hero_image_url,
        is_featured: businessSchema.businesses.is_featured,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        }
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id))
      .where(eq(businessSchema.businesses.is_active, true));

    if (category) {
      query = query.where(and(
        eq(businessSchema.businesses.is_active, true),
        eq(businessSchema.businesses.category_id, parseInt(category))
      ));
    }

    if (search) {
      query = query.where(and(
        eq(businessSchema.businesses.is_active, true),
        sql`(${businessSchema.businesses.name} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businesses.short_description} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businesses.address} ILIKE ${`%${search}%`})`
      ));
    }

    if (featured) {
      query = query.where(and(
        eq(businessSchema.businesses.is_active, true),
        eq(businessSchema.businesses.is_featured, true)
      ));
    }

    const businesses = await query
      .orderBy(desc(businessSchema.businesses.is_featured), desc(businessSchema.businesses.created_at))
      .limit(limit)
      .offset(offset);

    // Get total count
    let countQuery = db.select({ total: count() }).from(businessSchema.businesses)
      .where(eq(businessSchema.businesses.is_active, true));

    if (category) {
      countQuery = countQuery.where(and(
        eq(businessSchema.businesses.is_active, true),
        eq(businessSchema.businesses.category_id, parseInt(category))
      ));
    }

    if (search) {
      countQuery = countQuery.where(and(
        eq(businessSchema.businesses.is_active, true),
        sql`(${businessSchema.businesses.name} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businesses.short_description} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businesses.address} ILIKE ${`%${search}%`})`
      ));
    }

    if (featured) {
      countQuery = countQuery.where(and(
        eq(businessSchema.businesses.is_active, true),
        eq(businessSchema.businesses.is_featured, true)
      ));
    }

    const [{ total }] = await countQuery;

    return c.json({
      businesses,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching businesses:', error);
    return c.json({ error: 'Failed to fetch businesses' }, 500);
  }
});

// Get business by slug (public)
api.get('/businesses/:slug', async (c) => {
  try {
    const slug = c.req.param('slug');
    const db = await getDatabase();

    const [business] = await db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        description: businessSchema.businesses.description,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        latitude: businessSchema.businesses.latitude,
        longitude: businessSchema.businesses.longitude,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        logo_url: businessSchema.businesses.logo_url,
        hero_image_url: businessSchema.businesses.hero_image_url,
        is_featured: businessSchema.businesses.is_featured,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        }
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id))
      .where(and(
        eq(businessSchema.businesses.slug, slug),
        eq(businessSchema.businesses.is_active, true)
      ))
      .limit(1);

    if (!business) {
      return c.json({ error: 'Business not found' }, 404);
    }

    // Get business hours
    const hours = await db
      .select()
      .from(businessSchema.businessHours)
      .where(eq(businessSchema.businessHours.business_id, business.id))
      .orderBy(businessSchema.businessHours.day_of_week);

    // Get approved reviews
    const reviews = await db
      .select()
      .from(businessSchema.reviews)
      .where(and(
        eq(businessSchema.reviews.business_id, business.id),
        eq(businessSchema.reviews.is_approved, true)
      ))
      .orderBy(desc(businessSchema.reviews.created_at))
      .limit(10);

    return c.json({
      business: {
        ...business,
        hours,
        reviews
      }
    });
  } catch (error) {
    console.error('Error fetching business:', error);
    return c.json({ error: 'Failed to fetch business' }, 500);
  }
});

// Submit business application (public)
api.post('/applications', async (c) => {
  try {
    const body = await c.req.json();
    const db = await getDatabase();

    // Validate required fields
    const requiredFields = ['business_name', 'category_id', 'description', 'address', 'email', 'contact_person'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return c.json({ error: `Missing required field: ${field}` }, 400);
      }
    }

    const [application] = await db
      .insert(businessSchema.businessApplications)
      .values({
        business_name: body.business_name,
        category_id: body.category_id,
        description: body.description,
        address: body.address,
        latitude: body.latitude || null,
        longitude: body.longitude || null,
        phone: body.phone || null,
        email: body.email,
        website: body.website || null,
        contact_person: body.contact_person,
        additional_info: body.additional_info || null,
        status: 'pending',
      })
      .returning();

    return c.json({
      message: 'Application submitted successfully',
      application_id: application.id
    }, 201);
  } catch (error) {
    console.error('Error submitting application:', error);
    return c.json({ error: 'Failed to submit application' }, 500);
  }
});

// Submit review (public)
api.post('/reviews', async (c) => {
  try {
    const body = await c.req.json();
    const db = await getDatabase();

    // Validate required fields
    if (!body.business_id || !body.rating || !body.author_name) {
      return c.json({ error: 'Missing required fields: business_id, rating, author_name' }, 400);
    }

    if (body.rating < 1 || body.rating > 5) {
      return c.json({ error: 'Rating must be between 1 and 5' }, 400);
    }

    const [review] = await db
      .insert(businessSchema.reviews)
      .values({
        business_id: body.business_id,
        rating: body.rating,
        comment: body.comment || null,
        author_name: body.author_name,
        author_email: body.author_email || null,
        is_verified: false,
        is_approved: false, // Reviews need admin approval
      })
      .returning();

    return c.json({
      message: 'Review submitted successfully and is pending approval',
      review_id: review.id
    }, 201);
  } catch (error) {
    console.error('Error submitting review:', error);
    return c.json({ error: 'Failed to submit review' }, 500);
  }
});

// Database test route
api.get('/test-db', async (c) => {
  try {
    const isHealthy = await testDatabaseConnection();
    const db = await getDatabase();

    const result = await db.select().from(schema.users).limit(5);

    return c.json({
      message: 'Database connection successful!',
      users: result,
      connectionHealthy: isHealthy,
      usingLocalDatabase: !getDatabaseUrl(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Database test error:', error);
    return c.json({
      error: 'Database connection failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Protected routes - require authentication
const protectedRoutes = new Hono();

protectedRoutes.use('*', authMiddleware);

protectedRoutes.get('/me', (c) => {
  const user = c.get('user');
  return c.json({
    user,
    message: 'You are authenticated!',
  });
});

// ===== ADMIN ROUTES =====
// Create main admin routes with admin middleware
const mainAdminRoutes = new Hono();
mainAdminRoutes.use('*', adminMiddleware);

// Mount modular admin routes
mainAdminRoutes.route('/', adminRoutes);
mainAdminRoutes.route('/businesses', businessRoutes);
mainAdminRoutes.route('/categories', categoryRoutes);
mainAdminRoutes.route('/reviews', reviewRoutes);
mainAdminRoutes.route('/users', userRoutes);
mainAdminRoutes.route('/applications', applicationRoutes);

// Existing admin applications route (keeping for backward compatibility)
mainAdminRoutes.get('/applications', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const status = c.req.query('status') || 'pending';
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    const applications = await db
      .select({
        id: businessSchema.businessApplications.id,
        business_name: businessSchema.businessApplications.business_name,
        description: businessSchema.businessApplications.description,
        address: businessSchema.businessApplications.address,
        phone: businessSchema.businessApplications.phone,
        email: businessSchema.businessApplications.email,
        website: businessSchema.businessApplications.website,
        contact_person: businessSchema.businessApplications.contact_person,
        status: businessSchema.businessApplications.status,
        admin_notes: businessSchema.businessApplications.admin_notes,
        submitted_at: businessSchema.businessApplications.submitted_at,
        reviewed_at: businessSchema.businessApplications.reviewed_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        }
      })
      .from(businessSchema.businessApplications)
      .leftJoin(businessSchema.categories, eq(businessSchema.businessApplications.category_id, businessSchema.categories.id))
      .where(eq(businessSchema.businessApplications.status, status))
      .orderBy(desc(businessSchema.businessApplications.submitted_at))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await db
      .select({ total: count() })
      .from(businessSchema.businessApplications)
      .where(eq(businessSchema.businessApplications.status, status));

    return c.json({
      applications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching applications:', error);
    return c.json({ error: 'Failed to fetch applications' }, 500);
  }
});

// Admin Application Approval/Rejection
mainAdminRoutes.put('/applications/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const body = await c.req.json();
    const db = await getDatabase();

    if (!body.action || !['approve', 'reject'].includes(body.action)) {
      return c.json({ error: 'Action must be "approve" or "reject"' }, 400);
    }

    // Get the application
    const [application] = await db
      .select()
      .from(businessSchema.businessApplications)
      .where(eq(businessSchema.businessApplications.id, id))
      .limit(1);

    if (!application) {
      return c.json({ error: 'Application not found' }, 404);
    }

    if (application.status !== 'pending') {
      return c.json({ error: 'Application has already been processed' }, 400);
    }

    if (body.action === 'approve') {
      // Create business from application
      const [newBusiness] = await db
        .insert(businessSchema.businesses)
        .values({
          name: application.business_name,
          slug: application.business_name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
          category_id: application.category_id,
          description: application.description,
          short_description: application.description.substring(0, 500),
          address: application.address,
          latitude: application.latitude,
          longitude: application.longitude,
          phone: application.phone,
          email: application.email,
          website: application.website,
          is_featured: false,
          is_active: true,
        })
        .returning();

      // Update application status
      await db
        .update(businessSchema.businessApplications)
        .set({
          status: 'approved',
          admin_notes: body.notes || null,
          reviewed_at: new Date(),
          approved_business_id: newBusiness.id,
        })
        .where(eq(businessSchema.businessApplications.id, id));

      return c.json({
        message: 'Application approved and business created',
        business: newBusiness
      });
    } else {
      // Reject application
      await db
        .update(businessSchema.businessApplications)
        .set({
          status: 'rejected',
          admin_notes: body.notes || null,
          reviewed_at: new Date(),
        })
        .where(eq(businessSchema.businessApplications.id, id));

      return c.json({
        message: 'Application rejected'
      });
    }
  } catch (error) {
    console.error('Error processing application:', error);
    return c.json({ error: 'Failed to process application' }, 500);
  }
});

// Mount the protected routes under /protected
api.route('/protected', protectedRoutes);

// Mount the admin routes under /admin
api.route('/admin', mainAdminRoutes);

// Mount the API router
app.route('/api/v1', api);

export default app;
