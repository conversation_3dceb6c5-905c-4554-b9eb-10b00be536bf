import 'dotenv/config';
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { authMiddleware, adminMiddleware } from './middleware/auth';
import { getDatabase, testDatabaseConnection } from './lib/db';
import { setEnvContext, clearEnvContext, getDatabaseUrl } from './lib/env';
import * as schema from './schema/users';
import * as businessSchema from './schema/business';
import { eq, and, like, desc, asc, count, sql } from 'drizzle-orm';

type Env = {
  RUNTIME?: string;
  [key: string]: any;
};

const app = new Hono<{ Bindings: Env }>();

// In Node.js environment, set environment context from process.env
if (typeof process !== 'undefined' && process.env) {
  setEnvContext(process.env);
}

// Environment context middleware - detect runtime using RUNTIME env var
app.use('*', async (c, next) => {
  if (c.env?.RUNTIME === 'cloudflare') {
    setEnvContext(c.env);
  }
  
  await next();
  // No need to clear context - env vars are the same for all requests
  // In fact, clearing the context would cause the env vars to potentially be unset for parallel requests
});

// Middleware
app.use('*', logger());
app.use('*', cors());

// Health check route - public
app.get('/', (c) => c.json({ status: 'ok', message: 'API is running' }));

// API routes
const api = new Hono();

// Public routes go here (if any)
api.get('/hello', (c) => {
  return c.json({
    message: 'Hello from Hono!',
  });
});

// ===== BUSINESS DIRECTORY API ROUTES =====

// Public Business Routes
api.get('/businesses', async (c) => {
  try {
    const db = await getDatabase();
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const category = c.req.query('category');
    const featured = c.req.query('featured');
    const offset = (page - 1) * limit;

    let query = db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        latitude: businessSchema.businesses.latitude,
        longitude: businessSchema.businesses.longitude,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        logo_url: businessSchema.businesses.logo_url,
        hero_image_url: businessSchema.businesses.hero_image_url,
        is_featured: businessSchema.businesses.is_featured,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
          icon: businessSchema.categories.icon,
        }
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id))
      .where(eq(businessSchema.businesses.is_active, true));

    if (category) {
      query = query.where(eq(businessSchema.categories.slug, category));
    }

    if (featured === 'true') {
      query = query.where(eq(businessSchema.businesses.is_featured, true));
    }

    const businesses = await query
      .orderBy(desc(businessSchema.businesses.is_featured), desc(businessSchema.businesses.created_at))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const [{ total }] = await db
      .select({ total: count() })
      .from(businessSchema.businesses)
      .where(eq(businessSchema.businesses.is_active, true));

    return c.json({
      businesses,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching businesses:', error);
    return c.json({ error: 'Failed to fetch businesses' }, 500);
  }
});

api.get('/businesses/:slug', async (c) => {
  try {
    const slug = c.req.param('slug');
    const db = await getDatabase();

    const [business] = await db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        description: businessSchema.businesses.description,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        latitude: businessSchema.businesses.latitude,
        longitude: businessSchema.businesses.longitude,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        logo_url: businessSchema.businesses.logo_url,
        hero_image_url: businessSchema.businesses.hero_image_url,
        is_featured: businessSchema.businesses.is_featured,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
          icon: businessSchema.categories.icon,
          description: businessSchema.categories.description,
        }
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id))
      .where(and(
        eq(businessSchema.businesses.slug, slug),
        eq(businessSchema.businesses.is_active, true)
      ))
      .limit(1);

    if (!business) {
      return c.json({ error: 'Business not found' }, 404);
    }

    // Get business hours
    const hours = await db
      .select()
      .from(businessSchema.businessHours)
      .where(eq(businessSchema.businessHours.business_id, business.id))
      .orderBy(asc(businessSchema.businessHours.day_of_week));

    // Get business images
    const images = await db
      .select()
      .from(businessSchema.businessImages)
      .where(eq(businessSchema.businessImages.business_id, business.id))
      .orderBy(desc(businessSchema.businessImages.is_primary), asc(businessSchema.businessImages.display_order));

    // Get recent reviews
    const reviews = await db
      .select()
      .from(businessSchema.reviews)
      .where(and(
        eq(businessSchema.reviews.business_id, business.id),
        eq(businessSchema.reviews.is_approved, true)
      ))
      .orderBy(desc(businessSchema.reviews.created_at))
      .limit(10);

    return c.json({
      ...business,
      hours,
      images,
      reviews
    });
  } catch (error) {
    console.error('Error fetching business:', error);
    return c.json({ error: 'Failed to fetch business' }, 500);
  }
});

// Categories Routes
api.get('/categories', async (c) => {
  try {
    const db = await getDatabase();

    const categories = await db
      .select({
        id: businessSchema.categories.id,
        name: businessSchema.categories.name,
        slug: businessSchema.categories.slug,
        icon: businessSchema.categories.icon,
        description: businessSchema.categories.description,
        business_count: count(businessSchema.businesses.id)
      })
      .from(businessSchema.categories)
      .leftJoin(businessSchema.businesses, and(
        eq(businessSchema.categories.id, businessSchema.businesses.category_id),
        eq(businessSchema.businesses.is_active, true)
      ))
      .where(eq(businessSchema.categories.is_active, true))
      .groupBy(businessSchema.categories.id)
      .orderBy(asc(businessSchema.categories.name));

    return c.json({ categories });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return c.json({ error: 'Failed to fetch categories' }, 500);
  }
});

api.get('/categories/:slug', async (c) => {
  try {
    const slug = c.req.param('slug');
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    // Get category details
    const [category] = await db
      .select()
      .from(businessSchema.categories)
      .where(and(
        eq(businessSchema.categories.slug, slug),
        eq(businessSchema.categories.is_active, true)
      ))
      .limit(1);

    if (!category) {
      return c.json({ error: 'Category not found' }, 404);
    }

    // Get businesses in this category
    const businesses = await db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        latitude: businessSchema.businesses.latitude,
        longitude: businessSchema.businesses.longitude,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        logo_url: businessSchema.businesses.logo_url,
        hero_image_url: businessSchema.businesses.hero_image_url,
        is_featured: businessSchema.businesses.is_featured,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
      })
      .from(businessSchema.businesses)
      .where(and(
        eq(businessSchema.businesses.category_id, category.id),
        eq(businessSchema.businesses.is_active, true)
      ))
      .orderBy(desc(businessSchema.businesses.is_featured), desc(businessSchema.businesses.created_at))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const [{ total }] = await db
      .select({ total: count() })
      .from(businessSchema.businesses)
      .where(and(
        eq(businessSchema.businesses.category_id, category.id),
        eq(businessSchema.businesses.is_active, true)
      ));

    return c.json({
      category,
      businesses,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching category businesses:', error);
    return c.json({ error: 'Failed to fetch category businesses' }, 500);
  }
});

// Search Route
api.get('/search', async (c) => {
  try {
    const query = c.req.query('q');
    const category = c.req.query('category');
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    if (!query || query.trim().length < 2) {
      return c.json({ error: 'Search query must be at least 2 characters' }, 400);
    }

    let searchQuery = db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        latitude: businessSchema.businesses.latitude,
        longitude: businessSchema.businesses.longitude,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        logo_url: businessSchema.businesses.logo_url,
        hero_image_url: businessSchema.businesses.hero_image_url,
        is_featured: businessSchema.businesses.is_featured,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
          icon: businessSchema.categories.icon,
        }
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id))
      .where(and(
        eq(businessSchema.businesses.is_active, true),
        sql`(
          ${businessSchema.businesses.name} ILIKE ${`%${query}%`} OR
          ${businessSchema.businesses.description} ILIKE ${`%${query}%`} OR
          ${businessSchema.businesses.short_description} ILIKE ${`%${query}%`} OR
          ${businessSchema.businesses.address} ILIKE ${`%${query}%`}
        )`
      ));

    if (category) {
      searchQuery = searchQuery.where(eq(businessSchema.categories.slug, category));
    }

    const businesses = await searchQuery
      .orderBy(desc(businessSchema.businesses.is_featured), desc(businessSchema.businesses.created_at))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    let countQuery = db
      .select({ total: count() })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id))
      .where(and(
        eq(businessSchema.businesses.is_active, true),
        sql`(
          ${businessSchema.businesses.name} ILIKE ${`%${query}%`} OR
          ${businessSchema.businesses.description} ILIKE ${`%${query}%`} OR
          ${businessSchema.businesses.short_description} ILIKE ${`%${query}%`} OR
          ${businessSchema.businesses.address} ILIKE ${`%${query}%`}
        )`
      ));

    if (category) {
      countQuery = countQuery.where(eq(businessSchema.categories.slug, category));
    }

    const [{ total }] = await countQuery;

    return c.json({
      businesses,
      query,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error searching businesses:', error);
    return c.json({ error: 'Failed to search businesses' }, 500);
  }
});

// Business Application Submission
api.post('/applications', async (c) => {
  try {
    const body = await c.req.json();
    const db = await getDatabase();

    // Basic validation
    const required = ['business_name', 'category_id', 'description', 'address', 'email', 'contact_person'];
    for (const field of required) {
      if (!body[field]) {
        return c.json({ error: `${field} is required` }, 400);
      }
    }

    // Validate category exists
    const [category] = await db
      .select()
      .from(businessSchema.categories)
      .where(eq(businessSchema.categories.id, body.category_id))
      .limit(1);

    if (!category) {
      return c.json({ error: 'Invalid category' }, 400);
    }

    // Insert application
    const [application] = await db
      .insert(businessSchema.businessApplications)
      .values({
        business_name: body.business_name,
        category_id: body.category_id,
        description: body.description,
        address: body.address,
        latitude: body.latitude || null,
        longitude: body.longitude || null,
        phone: body.phone || null,
        email: body.email,
        website: body.website || null,
        contact_person: body.contact_person,
        status: 'pending'
      })
      .returning();

    return c.json({
      message: 'Application submitted successfully',
      application: {
        id: application.id,
        status: application.status,
        submitted_at: application.submitted_at
      }
    }, 201);
  } catch (error) {
    console.error('Error submitting application:', error);
    return c.json({ error: 'Failed to submit application' }, 500);
  }
});

// Review Submission
api.post('/reviews', async (c) => {
  try {
    const body = await c.req.json();
    const db = await getDatabase();

    // Basic validation
    const required = ['business_id', 'rating', 'author_name'];
    for (const field of required) {
      if (!body[field]) {
        return c.json({ error: `${field} is required` }, 400);
      }
    }

    // Validate rating
    if (body.rating < 1 || body.rating > 5) {
      return c.json({ error: 'Rating must be between 1 and 5' }, 400);
    }

    // Validate business exists
    const [business] = await db
      .select()
      .from(businessSchema.businesses)
      .where(and(
        eq(businessSchema.businesses.id, body.business_id),
        eq(businessSchema.businesses.is_active, true)
      ))
      .limit(1);

    if (!business) {
      return c.json({ error: 'Business not found' }, 404);
    }

    // Insert review
    const [review] = await db
      .insert(businessSchema.reviews)
      .values({
        business_id: body.business_id,
        rating: body.rating,
        comment: body.comment || null,
        author_name: body.author_name,
        author_email: body.author_email || null,
        is_verified: false,
        is_approved: false // Reviews need admin approval
      })
      .returning();

    return c.json({
      message: 'Review submitted successfully and is pending approval',
      review: {
        id: review.id,
        rating: review.rating,
        created_at: review.created_at
      }
    }, 201);
  } catch (error) {
    console.error('Error submitting review:', error);
    return c.json({ error: 'Failed to submit review' }, 500);
  }
});

// Database test route - public for testing
api.get('/db-test', async (c) => {
  try {
    // Use external DB URL if available, otherwise use local PostgreSQL database server
    // Note: In development, the port is dynamically allocated by port-manager.js
    const defaultLocalConnection = process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5502/postgres';
    const dbUrl = getDatabaseUrl() || defaultLocalConnection;
    
    const db = await getDatabase(dbUrl);
    const isHealthy = await testDatabaseConnection();
    
    if (!isHealthy) {
      return c.json({
        error: 'Database connection is not healthy',
        timestamp: new Date().toISOString(),
      }, 500);
    }
    
    const result = await db.select().from(schema.users).limit(5);
    
    return c.json({
      message: 'Database connection successful!',
      users: result,
      connectionHealthy: isHealthy,
      usingLocalDatabase: !getDatabaseUrl(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Database test error:', error);
    return c.json({
      error: 'Database connection failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Protected routes - require authentication
const protectedRoutes = new Hono();

protectedRoutes.use('*', authMiddleware);

protectedRoutes.get('/me', (c) => {
  const user = c.get('user');
  return c.json({
    user,
    message: 'You are authenticated!',
  });
});

// ===== ADMIN ROUTES =====
// Create admin routes with admin middleware
const adminRoutes = new Hono();
adminRoutes.use('*', adminMiddleware);

// Admin Business Management
adminRoutes.get('/businesses', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const status = c.req.query('status'); // active, inactive, all
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    let query = db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        is_featured: businessSchema.businesses.is_featured,
        is_active: businessSchema.businesses.is_active,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        }
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id));

    if (status === 'active') {
      query = query.where(eq(businessSchema.businesses.is_active, true));
    } else if (status === 'inactive') {
      query = query.where(eq(businessSchema.businesses.is_active, false));
    }

    const businesses = await query
      .orderBy(desc(businessSchema.businesses.created_at))
      .limit(limit)
      .offset(offset);

    // Get total count
    let countQuery = db.select({ total: count() }).from(businessSchema.businesses);
    if (status === 'active') {
      countQuery = countQuery.where(eq(businessSchema.businesses.is_active, true));
    } else if (status === 'inactive') {
      countQuery = countQuery.where(eq(businessSchema.businesses.is_active, false));
    }

    const [{ total }] = await countQuery;

    return c.json({
      businesses,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching admin businesses:', error);
    return c.json({ error: 'Failed to fetch businesses' }, 500);
  }
});

// Admin Applications Management
adminRoutes.get('/applications', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const status = c.req.query('status') || 'pending';
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    const applications = await db
      .select({
        id: businessSchema.businessApplications.id,
        business_name: businessSchema.businessApplications.business_name,
        description: businessSchema.businessApplications.description,
        address: businessSchema.businessApplications.address,
        phone: businessSchema.businessApplications.phone,
        email: businessSchema.businessApplications.email,
        website: businessSchema.businessApplications.website,
        contact_person: businessSchema.businessApplications.contact_person,
        status: businessSchema.businessApplications.status,
        admin_notes: businessSchema.businessApplications.admin_notes,
        submitted_at: businessSchema.businessApplications.submitted_at,
        reviewed_at: businessSchema.businessApplications.reviewed_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        }
      })
      .from(businessSchema.businessApplications)
      .leftJoin(businessSchema.categories, eq(businessSchema.businessApplications.category_id, businessSchema.categories.id))
      .where(eq(businessSchema.businessApplications.status, status))
      .orderBy(desc(businessSchema.businessApplications.submitted_at))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await db
      .select({ total: count() })
      .from(businessSchema.businessApplications)
      .where(eq(businessSchema.businessApplications.status, status));

    return c.json({
      applications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching applications:', error);
    return c.json({ error: 'Failed to fetch applications' }, 500);
  }
});

// Admin Application Approval/Rejection
adminRoutes.put('/applications/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const body = await c.req.json();
    const db = await getDatabase();

    if (!body.action || !['approve', 'reject'].includes(body.action)) {
      return c.json({ error: 'Action must be "approve" or "reject"' }, 400);
    }

    // Get the application
    const [application] = await db
      .select()
      .from(businessSchema.businessApplications)
      .where(eq(businessSchema.businessApplications.id, id))
      .limit(1);

    if (!application) {
      return c.json({ error: 'Application not found' }, 404);
    }

    if (application.status !== 'pending') {
      return c.json({ error: 'Application has already been processed' }, 400);
    }

    if (body.action === 'approve') {
      // Create business from application
      const [newBusiness] = await db
        .insert(businessSchema.businesses)
        .values({
          name: application.business_name,
          slug: application.business_name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
          category_id: application.category_id,
          description: application.description,
          short_description: application.description.substring(0, 500),
          address: application.address,
          latitude: application.latitude,
          longitude: application.longitude,
          phone: application.phone,
          email: application.email,
          website: application.website,
          is_featured: false,
          is_active: true,
        })
        .returning();

      // Update application status
      await db
        .update(businessSchema.businessApplications)
        .set({
          status: 'approved',
          admin_notes: body.notes || null,
          reviewed_at: new Date(),
          approved_business_id: newBusiness.id,
        })
        .where(eq(businessSchema.businessApplications.id, id));

      return c.json({
        message: 'Application approved and business created',
        business: newBusiness
      });
    } else {
      // Reject application
      await db
        .update(businessSchema.businessApplications)
        .set({
          status: 'rejected',
          admin_notes: body.notes || null,
          reviewed_at: new Date(),
        })
        .where(eq(businessSchema.businessApplications.id, id));

      return c.json({
        message: 'Application rejected'
      });
    }
  } catch (error) {
    console.error('Error processing application:', error);
    return c.json({ error: 'Failed to process application' }, 500);
  }
});

// Mount the protected routes under /protected
api.route('/protected', protectedRoutes);

// Mount the admin routes under /admin
api.route('/admin', adminRoutes);

// Mount the API router
app.route('/api/v1', api);

export default app; 